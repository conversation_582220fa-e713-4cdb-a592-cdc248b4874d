# Production Environment Variables Template
# Copy this to .env on your production server and update the values

# Application
NODE_ENV=production
BACKEND_PORT=5000
ENABLE_AUTH=TRUE
JWT_SECRET=your-super-secure-jwt-secret-here
JWT_EXPIRES=7d

# External Database Configuration
# Update these values with your external PostgreSQL database details
DB_HOST=your-external-db-host
DB_PORT=5432
DB_NAME=fes_crm_prod
DB_USER=your-db-username
DB_PASSWORD=your-db-password

# Full database URL (update with your external database details)
DATABASE_URL="postgres://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"

# Performance Tuning
NODE_OPTIONS="--max-old-space-size=2048"
UV_THREADPOOL_SIZE=128

# Monitoring and Alerting
SLACK_WEBHOOK=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
ALERT_EMAIL=<EMAIL>
METRICS_ENDPOINT=https://your-metrics-endpoint.com/api/metrics

# Load Balancer Configuration
PROD_HOST=your-production-domain.com

# Deployment Configuration
DEPLOYMENT_VERSION=latest
DEPLOYMENT_TIMESTAMP=$(date -Iseconds)

# Security Configuration
TRUSTED_PROXIES=127.0.0.1,::1
CORS_ORIGINS=https://your-frontend-domain.com

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_MAX_SIZE=100m
LOG_MAX_FILES=5