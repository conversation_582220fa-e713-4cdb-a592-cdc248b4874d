name: Deploy on Dev Server
on:
  push:
    branches: [develop]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: SSH Remove Existing Files Except .env
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.AWS_HOST }}
          username: ${{ secrets.AWS_USERNAME }}
          key: ${{ secrets.AWS_SSH_KEY }}
          port: 22
          script: |
            cd /home/<USER>/fes-crm-backend
            # Remove all files except .env
            find . ! -name '.env' -type f -delete
            find . ! -name '.env' -type d -empty -delete

      - name: Copy files to AWS EC2
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.AWS_HOST }}
          username: ${{ secrets.AWS_USERNAME }}
          key: ${{ secrets.AWS_SSH_KEY }}
          port: 22
          source: '.'
          target: '/home/<USER>/fes-crm-backend'

      - name: SSH Set File Permissions
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.<PERSON>WS_HOST }}
          username: ${{ secrets.AWS_USERNAME }}
          key: ${{ secrets.AWS_SSH_KEY }}
          port: 22
          script: |
            cd /home/<USER>/fes-crm-backend
            find . -type d -exec chmod 755 {} \;
            find . -type f -exec chmod 644 {} \;
            sudo chmod +x entrypoint.sh
            sudo chown -R $USER:$USER ~/fes-crm-backend

      - name: SSH Deploy with Docker
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.AWS_HOST }}
          username: ${{ secrets.AWS_USERNAME }}
          key: ${{ secrets.AWS_SSH_KEY }}
          port: 22
          script: |
            cd /home/<USER>/fes-crm-backend
            sudo docker compose down --rmi all --volumes --remove-orphans
            sudo docker system prune -a -f
            sudo docker compose up -d --build
            # sudo docker compose down  
            # sudo docker compose up -d --build
            sudo docker image prune -f
            sudo docker container prune -f
            sudo docker builder prune -f
