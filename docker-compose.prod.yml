services:
  backend:
    image: fes-crm-backend:latest
    container_name: fes_crm_prod
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DATABASE_URL: ${DATABASE_URL}
      BACKEND_PORT: 5000
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRES: ${JWT_EXPIRES}
      ENABLE_AUTH: ${ENABLE_AUTH}
      # Performance tuning
      NODE_OPTIONS: "--max-old-space-size=2048"
      UV_THREADPOOL_SIZE: 128
    ports:
      - '5000:5000'
    volumes:
      - uploads:/usr/src/app/uploads
      - logs:/usr/src/app/logs
    networks:
      - fes_crm_network
    healthcheck:
      test: [
        "CMD-SHELL",
        "curl -f http://localhost:5000/ || exit 1"
      ]
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    labels:
      - "com.docker.compose.service=backend"
      - "com.docker.compose.project=fes-crm"
      - "deployment.version=${DEPLOYMENT_VERSION:-latest}"
      - "deployment.timestamp=${DEPLOYMENT_TIMESTAMP:-unknown}"
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
        labels: "service,version"
    security_opt:
      - no-new-privileges:true
    read_only: false
    tmpfs:
      - /tmp:noexec,nosuid,size=100m



  # Monitoring and logging
  watchtower:
    image: containrrr/watchtower:latest
    container_name: fes_crm_watchtower
    restart: unless-stopped
    environment:
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_POLL_INTERVAL=300
      - WATCHTOWER_INCLUDE_STOPPED=true
      - WATCHTOWER_REVIVE_STOPPED=false
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - fes_crm_network
    labels:
      - "com.docker.compose.service=watchtower"

networks:
  fes_crm_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
    labels:
      - "com.docker.compose.network=fes_crm"

volumes:
  uploads:
    driver: local
    labels:
      - "com.docker.compose.volume=uploads"
  logs:
    driver: local
    labels:
      - "com.docker.compose.volume=logs"

