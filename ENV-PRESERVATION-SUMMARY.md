# Environment File Preservation in Zero-Downtime Deployment

## 🎯 Overview

The zero-downtime deployment system has been enhanced to **automatically preserve and restore the `.env` file** during all deployment and rollback operations. This ensures that your production environment configuration remains intact throughout the deployment process.

## 🔧 How It Works

### 1. **GitHub Actions Workflow** (`.github/workflows/production.yml`)

#### Environment File Backup & Restoration
```bash
# Backup and preserve environment file
if [ -f "${APP_DIR}/.env" ]; then
  # Create backup of current .env
  cp "${APP_DIR}/.env" "${NEW_DIR}/.env.backup"
  cp "${APP_DIR}/.env" "${NEW_DIR}/.env"
  log "Environment file backed up and copied from current deployment"
else
  error "No .env file found in current deployment"
  error "Please ensure .env file exists on the server before deployment"
  exit 1
fi
```

#### Final Environment File Restoration
```bash
# Ensure .env is in place and has correct permissions
if [ -f "${APP_DIR}/.env" ]; then
  sudo chown $USER:$USER "${APP_DIR}/.env"
  chmod 600 "${APP_DIR}/.env"
  log "Environment file restored with correct permissions"
else
  # Restore from backup if available
  if [ -f "/tmp/.env.backup" ]; then
    cp "/tmp/.env.backup" "${APP_DIR}/.env"
    sudo chown $USER:$USER "${APP_DIR}/.env"
    chmod 600 "${APP_DIR}/.env"
    log "Environment file restored from backup"
  fi
fi
```

### 2. **Manual Deployment Script** (`scripts/zero-downtime-deploy.sh`)

#### Pre-Deployment Environment Check
```bash
# Ensure .env is preserved
if [[ -f ".env" ]]; then
    cp ".env" "/tmp/.env.deployment.backup"
    log "Environment file backed up to /tmp/.env.deployment.backup"
else
    error "No .env file found - deployment cannot proceed"
    error "Please ensure .env file exists before deployment"
    exit 1
fi
```

### 3. **Rollback Script** (`scripts/rollback.sh`)

#### Environment File Preservation Functions
```bash
# Preserve .env file function
preserve_env_file() {
    local backup_location="$1"

    if [[ -f ".env" ]]; then
        cp ".env" "$backup_location"
        chmod 600 "$backup_location"
        log "Environment file preserved at $backup_location"
        return 0
    else
        warn "No .env file found to preserve"
        return 1
    fi
}

# Restore .env file function
restore_env_file() {
    local backup_location="$1"

    if [[ -f "$backup_location" ]]; then
        cp "$backup_location" ".env"
        chmod 600 ".env"
        log "Environment file restored from $backup_location"
        return 0
    else
        warn "No preserved .env file found at $backup_location"
        return 1
    fi
}
```

## 📋 Deployment Process with Environment Preservation

### 1. **Automatic Deployment** (GitHub Actions)
1. ✅ **Check for .env**: Verifies `.env` exists on server
2. ✅ **Backup .env**: Creates multiple backups of current `.env`
3. ✅ **Deploy Application**: Runs blue-green deployment
4. ✅ **Restore .env**: Ensures `.env` is properly restored
5. ✅ **Set Permissions**: Applies correct file permissions (600)
6. ✅ **Verify Restoration**: Confirms `.env` is in place

### 2. **Manual Deployment**
```bash
# Deploy with environment preservation
./scripts/zero-downtime-deploy.sh -v latest

# The script will:
# - Check for .env file existence
# - Create backup before deployment
# - Preserve .env throughout the process
# - Restore .env after successful deployment
```

### 3. **Rollback Operations**
```bash
# List backups (shows .env status)
./scripts/rollback.sh -l

# Rollback with .env preservation
./scripts/rollback.sh -v abc123

# The script will:
# - Preserve current .env before rollback
# - Perform rollback operation
# - Restore preserved .env file
# - Verify .env is properly restored
```

## 🔒 Security Features

### File Permissions
- **`.env` files are set to `600`** (read/write for owner only)
- **Ownership is properly set** to the deployment user
- **Backup files are also secured** with appropriate permissions

### Backup Strategy
- **Multiple backup locations**: `/tmp/.env.backup`, `/tmp/.env.deployment.backup`, etc.
- **Timestamped backups**: Each operation creates uniquely named backups
- **Automatic cleanup**: Temporary backups are removed after successful operations

## 🚨 Error Handling

### Missing .env File
```bash
if [ ! -f ".env" ]; then
    error "No .env file found in current deployment"
    error "Please ensure .env file exists on the server before deployment"
    exit 1
fi
```

### Restoration Failure
```bash
if [ ! -f "${APP_DIR}/.env" ]; then
    error "Environment file missing after deployment move"
    # Restore from backup if available
    if [ -f "/tmp/.env.backup" ]; then
        cp "/tmp/.env.backup" "${APP_DIR}/.env"
        log "Environment file restored from backup"
    else
        error "No backup .env file available for restoration"
        exit 1
    fi
fi
```

## 📝 Setup Instructions

### 1. **Initial Server Setup**
```bash
# On your production server, create the .env file
cd /root/fes-crm-backend
cp .env.prod.example .env

# Edit with your actual configuration
nano .env

# Set proper permissions
chmod 600 .env
```

### 2. **Required Environment Variables**
```env
# Database Configuration
DATABASE_URL="**********************************************/database_name"

# Application Configuration
JWT_SECRET=your-super-secure-jwt-secret
NODE_ENV=production
BACKEND_PORT=5000

# Monitoring (Optional)
SLACK_WEBHOOK=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
```

### 3. **Verification**
```bash
# Check .env file exists and has correct permissions
ls -la .env
# Should show: -rw------- 1 <USER> <GROUP> ... .env

# Test deployment (dry run)
./scripts/zero-downtime-deploy.sh -v latest -d
```

## ✅ Benefits

1. **🔒 Secure**: Environment files are never exposed or lost
2. **🔄 Automatic**: No manual intervention required
3. **🛡️ Safe**: Multiple backup strategies prevent data loss
4. **📊 Monitored**: All operations are logged and verified
5. **🚀 Zero-Downtime**: Environment preservation doesn't affect deployment speed
6. **🔧 Flexible**: Works with all deployment and rollback scenarios

## 🎯 Key Points

- **The `.env` file is NEVER overwritten** during deployments
- **Multiple backups are created** at different stages
- **Proper file permissions are maintained** (600)
- **All operations are logged** for audit purposes
- **Deployment fails safely** if `.env` cannot be preserved
- **Rollback operations preserve** the current `.env` file

Your production environment configuration is now fully protected during all deployment operations! 🎉
