# Zero-Downtime Production Deployment Guide

This guide explains the comprehensive zero-downtime deployment system for FES CRM Backend with industry-standard practices.

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   GitHub        │    │   Production     │    │   External          │
│   Actions       │───▶│   Server         │───▶│   PostgreSQL        │
│   (main branch) │    │   (Docker)       │    │   Database          │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
```

## 📋 Prerequisites

1. **Production Server** with <PERSON><PERSON> and Docker Compose installed
2. **External PostgreSQL Database** accessible from production server
3. **GitHub Secrets** configured for deployment

## 🔧 Setup Steps

### 1. Configure GitHub Secrets

Add these secrets in your GitHub repository settings:

```
PROD_HOST=your-production-server-ip
PROD_USERNAME=ubuntu
PROD_SSH_KEY=your-ssh-private-key
```

### 2. Prepare Production Server

```bash
# Install Docker and Docker Compose (if not already installed)
sudo apt update
sudo apt install docker.io docker-compose-plugin

# Create application directory
sudo mkdir -p /home/<USER>/fes-crm-backend
sudo chown ubuntu:ubuntu /home/<USER>/fes-crm-backend
```

### 3. Configure Environment Variables

Create `.env` file on production server:

```bash
cd /home/<USER>/fes-crm-backend
cp .env.prod.example .env
# Edit .env with your actual database credentials
nano .env
```

Update the following variables:
```env
DATABASE_URL="**********************************************/database_name"
JWT_SECRET=your-super-secure-jwt-secret
```

## 🚀 Deployment

### Automatic Deployment
Push to `main` branch triggers automatic deployment via GitHub Actions.

### Manual Deployment
```bash
# On production server
cd /home/<USER>/fes-crm-backend
./scripts/deploy-prod.sh
```

## 📊 Monitoring

### Check Application Status
```bash
# View running containers
sudo docker compose -f docker-compose.prod.yml ps

# View logs
sudo docker compose -f docker-compose.prod.yml logs -f

# Check health
curl http://localhost:80/
```

### Container Management
```bash
# Restart application
sudo docker compose -f docker-compose.prod.yml restart

# Stop application
sudo docker compose -f docker-compose.prod.yml down

# Update and restart
sudo docker compose -f docker-compose.prod.yml up -d --build
```

## 🔒 Security Features

- **Non-root user**: Application runs as `nestjs` user inside container
- **Health checks**: Automatic container health monitoring
- **Resource limits**: Controlled memory and CPU usage
- **Network isolation**: Custom Docker network
- **External database**: No database credentials in container

## 🗂️ File Structure

```
fes-crm-backend/
├── docker-compose.prod.yml    # Production Docker Compose
├── Dockerfile.prod           # Production Dockerfile
├── entrypoint.prod.sh       # Production entrypoint script
├── .env                     # Production environment variables
└── scripts/
    └── deploy-prod.sh       # Manual deployment script
```

## 🚨 Troubleshooting

### Container Won't Start
```bash
# Check logs
sudo docker compose -f docker-compose.prod.yml logs

# Check if port is available
sudo netstat -tlnp | grep :80
```

### Database Connection Issues
```bash
# Test database connection from container
sudo docker compose -f docker-compose.prod.yml exec backend npx prisma db pull
```

### Application Health Check Fails
```bash
# Check if application is responding
curl -v http://localhost:80/

# Check container health
sudo docker inspect fes_crm_prod | grep Health -A 10
```

## 📈 Performance Optimization

- **Multi-stage build**: Reduces image size by ~60%
- **Production dependencies only**: Faster startup time
- **Health checks**: Automatic restart on failure
- **Resource monitoring**: Built-in container metrics

## 🔄 Rollback Procedure

If deployment fails:

```bash
# Stop current containers
sudo docker compose -f docker-compose.prod.yml down

# Pull previous working version from Git
git checkout previous-working-commit

# Rebuild and start
sudo docker compose -f docker-compose.prod.yml up -d --build
```
