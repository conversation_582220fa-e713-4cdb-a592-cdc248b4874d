#!/bin/bash

# Production Deployment Script
# This script can be run manually on the production server

set -e

echo "🚀 Starting Production Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${YELLOW}📋 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_error ".env file not found!"
    echo "Please create .env file from .env.prod.example template"
    exit 1
fi

print_step "Stopping existing containers..."
sudo docker compose -f docker-compose.prod.yml down --remove-orphans
print_success "Containers stopped"

print_step "Cleaning up unused Docker resources..."
sudo docker system prune -f
print_success "Cleanup completed"

print_step "Building and starting production containers..."
sudo docker compose -f docker-compose.prod.yml up -d --build
print_success "Containers started"

print_step "Cleaning up build cache..."
sudo docker builder prune -f
print_success "Build cache cleaned"

print_step "Checking container status..."
sudo docker compose -f docker-compose.prod.yml ps

print_step "Checking application health..."
sleep 10
if curl -f http://localhost:80/ > /dev/null 2>&1; then
    print_success "Application is healthy and running!"
else
    print_error "Application health check failed"
    echo "Check logs with: sudo docker compose -f docker-compose.prod.yml logs"
    exit 1
fi

print_success "🎉 Production deployment completed successfully!"
echo ""
echo "📊 Useful commands:"
echo "  View logs: sudo docker compose -f docker-compose.prod.yml logs -f"
echo "  Stop: sudo docker compose -f docker-compose.prod.yml down"
echo "  Restart: sudo docker compose -f docker-compose.prod.yml restart"
