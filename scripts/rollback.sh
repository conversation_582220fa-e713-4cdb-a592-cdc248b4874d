#!/bin/bash

# Rollback Script for FES CRM Backend
# Provides safe rollback to previous versions with .env preservation

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
APP_NAME="fes-crm-backend"
ROLLBACK_TIMEOUT=300

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Help function
show_help() {
    cat << EOF
Rollback Script for FES CRM Backend

Usage: $0 [OPTIONS]

OPTIONS:
    -v, --version VERSION    Specific version to rollback to
    -b, --backup BACKUP      Backup directory to restore from
    -l, --list               List available backups and versions
    -f, --force              Force rollback without confirmation
    -d, --dry-run            Show what would be done without executing
    -t, --timeout SECONDS    Rollback timeout (default: 300)
    -h, --help               Show this help message

EXAMPLES:
    $0 -l                    # List available backups
    $0 -v abc123             # Rollback to specific version
    $0 -b backup-20240101    # Rollback to specific backup
    $0 -f                    # Force rollback to latest backup
    $0 -d -v abc123          # Dry run rollback

NOTE: The .env file will be preserved during rollback operations.

EOF
}

# List available backups and versions
list_backups() {
    info "Available backups:"
    find /root -maxdepth 1 -name "${APP_NAME}-backup-*" -type d | sort -r | while read -r backup; do
        local backup_name=$(basename "$backup")
        local backup_date=$(echo "$backup_name" | sed "s/${APP_NAME}-backup-//")

        # Try to get version from backup
        local version="unknown"
        if [[ -f "$backup/.version" ]]; then
            version=$(cat "$backup/.version")
        fi

        echo "  $backup_name (version: $version, date: $backup_date)"
    done

    echo
    info "Current deployment:"
    if [[ -f ".version" ]]; then
        local current_version=$(cat .version)
        local deployment_time="unknown"
        if [[ -f ".deployment-time" ]]; then
            deployment_time=$(cat .deployment-time)
        fi
        echo "  Version: $current_version"
        echo "  Deployed: $deployment_time"
    else
        echo "  No version information available"
    fi

    echo
    info "Environment file status:"
    if [[ -f ".env" ]]; then
        echo "  .env file: Present (will be preserved during rollback)"
    else
        warn "  .env file: Missing (rollback may fail without proper configuration)"
    fi
}

# Health check function
health_check() {
    local retries=${1:-10}
    local interval=${2:-5}

    for i in $(seq 1 $retries); do
        if curl -f -s "http://localhost:5000/" > /dev/null 2>&1; then
            log "Health check passed (attempt $i/$retries)"
            return 0
        fi
        warn "Health check failed (attempt $i/$retries)"
        sleep $interval
    done

    error "Health check failed after $retries attempts"
    return 1
}

# Database rollback safety check
check_database_rollback_safety() {
    local target_version="$1"

    info "Checking database rollback safety for version $target_version..."

    # This is a placeholder for database rollback safety checks
    # In a real scenario, you would:
    # 1. Check if the target version's schema is compatible
    # 2. Verify no destructive migrations have been run since
    # 3. Ensure data integrity won't be compromised

    warn "Database rollback safety check is not implemented"
    warn "Manual verification of database compatibility is recommended"

    return 0
}

# Preserve .env file function
preserve_env_file() {
    local backup_location="$1"

    if [[ -f ".env" ]]; then
        cp ".env" "$backup_location"
        chmod 600 "$backup_location"
        log "Environment file preserved at $backup_location"
        return 0
    else
        warn "No .env file found to preserve"
        return 1
    fi
}

# Restore .env file function
restore_env_file() {
    local backup_location="$1"

    if [[ -f "$backup_location" ]]; then
        cp "$backup_location" ".env"
        chmod 600 ".env"
        log "Environment file restored from $backup_location"
        return 0
    else
        warn "No preserved .env file found at $backup_location"
        return 1
    fi
}

# Rollback to specific version
rollback_to_version() {
    local target_version="$1"
    local force=${2:-false}

    info "Rolling back to version: $target_version"

    # Check if the version exists as a Docker image
    if ! docker image inspect "${APP_NAME}:${target_version}" > /dev/null 2>&1; then
        error "Docker image ${APP_NAME}:${target_version} not found"
        return 1
    fi

    # Database rollback safety check
    if ! check_database_rollback_safety "$target_version"; then
        if [[ "$force" != "true" ]]; then
            error "Database rollback safety check failed. Use --force to override."
            return 1
        fi
        warn "Proceeding with rollback despite safety check failure"
    fi

    # Get current version for backup
    local current_version="unknown"
    if [[ -f ".version" ]]; then
        current_version=$(cat .version)
    fi

    # Preserve .env file
    local env_backup="/tmp/.env.rollback.$(date +%Y%m%d-%H%M%S)"
    preserve_env_file "$env_backup"

    # Create emergency backup before rollback
    local emergency_backup="/root/${APP_NAME}-emergency-backup-$(date +%Y%m%d-%H%M%S)"
    info "Creating emergency backup before rollback..."
    cp -r . "$emergency_backup"
    log "Emergency backup created: $emergency_backup"

    # Update docker-compose with target version
    info "Updating Docker Compose configuration..."
    sed -i "s|${APP_NAME}:.*|${APP_NAME}:${target_version}|g" docker-compose.prod.yml

    # Stop current deployment
    info "Stopping current deployment..."
    docker compose -f docker-compose.prod.yml down --remove-orphans

    # Start rollback version
    info "Starting rollback version..."
    docker compose -f docker-compose.prod.yml up -d

    # Wait for application to start
    info "Waiting for application to start..."
    sleep 30

    # Health check
    if ! health_check 15 10; then
        error "Rollback health check failed"

        # Attempt to restore from emergency backup
        warn "Attempting to restore from emergency backup..."
        docker compose -f docker-compose.prod.yml down --remove-orphans

        # Restore emergency backup
        cp -r "$emergency_backup"/* .

        # Restore .env file
        restore_env_file "$env_backup"

        docker compose -f docker-compose.prod.yml up -d

        if health_check 10 5; then
            log "Successfully restored from emergency backup"
        else
            error "Failed to restore from emergency backup - manual intervention required"
        fi

        return 1
    fi

    # Restore .env file after successful rollback
    restore_env_file "$env_backup"

    # Update version info
    echo "$target_version" > .version
    echo "$(date)" > .rollback-time
    echo "$current_version" > .previous-version

    log "Rollback to version $target_version completed successfully"

    # Cleanup emergency backup after successful rollback
    rm -rf "$emergency_backup"
    rm -f "$env_backup"

    return 0
}

# Rollback to specific backup
rollback_to_backup() {
    local backup_path="$1"
    local force=${2:-false}

    if [[ ! -d "$backup_path" ]]; then
        error "Backup directory not found: $backup_path"
        return 1
    fi

    info "Rolling back to backup: $backup_path"

    # Get version from backup if available
    local target_version="unknown"
    if [[ -f "$backup_path/.version" ]]; then
        target_version=$(cat "$backup_path/.version")
    fi

    # Database rollback safety check
    if [[ "$target_version" != "unknown" ]]; then
        if ! check_database_rollback_safety "$target_version"; then
            if [[ "$force" != "true" ]]; then
                error "Database rollback safety check failed. Use --force to override."
                return 1
            fi
            warn "Proceeding with rollback despite safety check failure"
        fi
    fi

    # Preserve current .env file
    local env_backup="/tmp/.env.rollback.$(date +%Y%m%d-%H%M%S)"
    preserve_env_file "$env_backup"

    # Create emergency backup before rollback
    local emergency_backup="/root/${APP_NAME}-emergency-backup-$(date +%Y%m%d-%H%M%S)"
    info "Creating emergency backup before rollback..."
    cp -r . "$emergency_backup"
    log "Emergency backup created: $emergency_backup"

    # Stop current deployment
    info "Stopping current deployment..."
    docker compose -f docker-compose.prod.yml down --remove-orphans

    # Restore from backup while preserving .env
    info "Restoring from backup..."

    # Remove current files (except .env)
    find . -mindepth 1 -maxdepth 1 ! -name '.env' -exec rm -rf {} +

    # Copy backup files
    cp -r "$backup_path"/* .

    # Restore preserved .env file
    restore_env_file "$env_backup"

    # Start restored version
    info "Starting restored version..."
    docker compose -f docker-compose.prod.yml up -d

    # Wait for application to start
    info "Waiting for application to start..."
    sleep 30

    # Health check
    if ! health_check 15 10; then
        error "Rollback health check failed"

        # Attempt to restore from emergency backup
        warn "Attempting to restore from emergency backup..."
        docker compose -f docker-compose.prod.yml down --remove-orphans

        # Remove failed restore
        find . -mindepth 1 -maxdepth 1 ! -name '.env' -exec rm -rf {} +

        # Restore emergency backup
        cp -r "$emergency_backup"/* .

        # Restore .env file
        restore_env_file "$env_backup"

        docker compose -f docker-compose.prod.yml up -d

        if health_check 10 5; then
            log "Successfully restored from emergency backup"
        else
            error "Failed to restore from emergency backup - manual intervention required"
        fi

        return 1
    fi

    # Update rollback info
    echo "$(date)" > .rollback-time
    echo "backup:$(basename "$backup_path")" > .rollback-source

    log "Rollback to backup $(basename "$backup_path") completed successfully"

    # Cleanup emergency backup after successful rollback
    rm -rf "$emergency_backup"
    rm -f "$env_backup"

    return 0
}

# Automatic rollback to latest backup
auto_rollback() {
    local force=${1:-false}

    info "Performing automatic rollback to latest backup..."

    # Find latest backup
    local latest_backup=$(find /root -maxdepth 1 -name "${APP_NAME}-backup-*" -type d | sort -r | head -1)

    if [[ -z "$latest_backup" ]]; then
        error "No backups found for automatic rollback"
        return 1
    fi

    info "Latest backup found: $(basename "$latest_backup")"

    rollback_to_backup "$latest_backup" "$force"
}

# Send notification
send_notification() {
    local message="$1"
    local severity="${2:-info}"

    # Slack notification
    if [[ -n "${SLACK_WEBHOOK:-}" ]]; then
        local emoji="ℹ️"
        local color="good"

        case "$severity" in
            "success") emoji="✅"; color="good" ;;
            "warning") emoji="⚠️"; color="warning" ;;
            "error") emoji="❌"; color="danger" ;;
        esac

        curl -X POST -H 'Content-type: application/json' \
            --data "{
              \"attachments\": [{
                \"color\": \"$color\",
                \"title\": \"$emoji FES CRM Backend Rollback\",
                \"text\": \"$message\",
                \"footer\": \"Rollback Script\",
                \"ts\": $(date +%s)
              }]
            }" \
            "$SLACK_WEBHOOK" > /dev/null 2>&1 || warn "Failed to send Slack notification"
    fi
}

# Main function
main() {
    local target_version=""
    local backup_path=""
    local list_only=false
    local force=false
    local dry_run=false

    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -v|--version)
                target_version="$2"
                shift 2
                ;;
            -b|--backup)
                backup_path="$2"
                shift 2
                ;;
            -l|--list)
                list_only=true
                shift
                ;;
            -f|--force)
                force=true
                shift
                ;;
            -d|--dry-run)
                dry_run=true
                shift
                ;;
            -t|--timeout)
                ROLLBACK_TIMEOUT="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # Change to project root
    cd "$PROJECT_ROOT"

    # Load environment variables
    if [[ -f ".env" ]]; then
        set -a
        source ".env"
        set +a
    fi

    # List backups and exit
    if [[ "$list_only" == "true" ]]; then
        list_backups
        exit 0
    fi

    # Dry run mode
    if [[ "$dry_run" == "true" ]]; then
        info "DRY RUN MODE - No actual changes will be made"
        info "The .env file will be preserved during rollback"

        if [[ -n "$target_version" ]]; then
            info "Would rollback to version: $target_version"
        elif [[ -n "$backup_path" ]]; then
            info "Would rollback to backup: $backup_path"
        else
            info "Would perform automatic rollback to latest backup"
        fi

        exit 0
    fi

    # Check if .env file exists
    if [[ ! -f ".env" ]]; then
        warn "No .env file found in current deployment"
        warn "Rollback will proceed but may require manual configuration"
    fi

    # Confirmation prompt
    if [[ "$force" != "true" ]]; then
        echo
        warn "This will rollback the current deployment!"
        info "The .env file will be preserved during rollback"

        if [[ -n "$target_version" ]]; then
            info "Target version: $target_version"
        elif [[ -n "$backup_path" ]]; then
            info "Target backup: $backup_path"
        else
            info "Will rollback to latest available backup"
        fi

        echo
        read -p "Continue with rollback? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            warn "Rollback cancelled by user"
            exit 0
        fi
    fi

    # Perform rollback
    local rollback_success=false

    if [[ -n "$target_version" ]]; then
        if rollback_to_version "$target_version" "$force"; then
            rollback_success=true
            send_notification "Successfully rolled back to version $target_version" "success"
        else
            send_notification "Failed to rollback to version $target_version" "error"
        fi
    elif [[ -n "$backup_path" ]]; then
        # Convert relative path to absolute if needed
        if [[ "$backup_path" != /* ]]; then
            backup_path="/root/$backup_path"
        fi

        if rollback_to_backup "$backup_path" "$force"; then
            rollback_success=true
            send_notification "Successfully rolled back to backup $(basename "$backup_path")" "success"
        else
            send_notification "Failed to rollback to backup $(basename "$backup_path")" "error"
        fi
    else
        if auto_rollback "$force"; then
            rollback_success=true
            send_notification "Successfully performed automatic rollback" "success"
        else
            send_notification "Failed to perform automatic rollback" "error"
        fi
    fi

    if [[ "$rollback_success" == "true" ]]; then
        log "Rollback completed successfully"

        # Display final status
        info "Final deployment status:"
        docker compose -f docker-compose.prod.yml ps

        # Verify .env file
        if [[ -f ".env" ]]; then
            log "Environment file successfully preserved"
        else
            warn "Environment file missing after rollback - manual configuration required"
        fi

        exit 0
    else
        error "Rollback failed"
        exit 1
    fi
}

# Run main function
main "$@"
