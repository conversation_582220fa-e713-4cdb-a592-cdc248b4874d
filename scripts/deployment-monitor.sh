#!/bin/bash

# Deployment Monitoring Script
# Monitors application health and sends alerts

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
APP_NAME="fes-crm-backend"
HEALTH_CHECK_INTERVAL=30
ALERT_THRESHOLD=3
LOG_FILE="/var/log/fes-crm-monitor.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() {
    local msg="[$(date +'%Y-%m-%d %H:%M:%S')] $1"
    echo -e "${GREEN}${msg}${NC}"
    echo "$msg" >> "$LOG_FILE"
}

warn() {
    local msg="[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1"
    echo -e "${YELLOW}${msg}${NC}"
    echo "$msg" >> "$LOG_FILE"
}

error() {
    local msg="[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1"
    echo -e "${RED}${msg}${NC}"
    echo "$msg" >> "$LOG_FILE"
}

info() {
    local msg="[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1"
    echo -e "${BLUE}${msg}${NC}"
    echo "$msg" >> "$LOG_FILE"
}

# Load environment variables
if [[ -f "/root/fes-crm-backend/.env" ]]; then
    set -a
    source "/root/fes-crm-backend/.env"
    set +a
fi

# Slack notification function
send_slack_alert() {
    local message="$1"
    local severity="${2:-warning}"
    local webhook_url="${SLACK_WEBHOOK:-}"

    if [[ -z "$webhook_url" ]]; then
        warn "Slack webhook not configured, skipping notification"
        return 0
    fi

    local color="warning"
    local emoji="⚠️"

    case "$severity" in
        "critical")
            color="danger"
            emoji="🚨"
            ;;
        "warning")
            color="warning"
            emoji="⚠️"
            ;;
        "info")
            color="good"
            emoji="ℹ️"
            ;;
        "success")
            color="good"
            emoji="✅"
            ;;
    esac

    local payload=$(cat << EOF
{
  "attachments": [{
    "color": "$color",
    "title": "$emoji FES CRM Backend Alert",
    "text": "$message",
    "fields": [
      {"title": "Server", "value": "$(hostname)", "short": true},
      {"title": "Time", "value": "$(date)", "short": true}
    ],
    "footer": "FES CRM Monitor",
    "ts": $(date +%s)
  }]
}
EOF
    )

    curl -X POST -H 'Content-type: application/json' \
        --data "$payload" \
        "$webhook_url" > /dev/null 2>&1 || warn "Failed to send Slack notification"
}

# Email notification function
send_email_alert() {
    local subject="$1"
    local message="$2"
    local email="${ALERT_EMAIL:-}"

    if [[ -z "$email" ]]; then
        warn "Alert email not configured, skipping email notification"
        return 0
    fi

    if command -v mail > /dev/null 2>&1; then
        echo "$message" | mail -s "$subject" "$email" || warn "Failed to send email notification"
    else
        warn "Mail command not available, skipping email notification"
    fi
}

# Health check function
check_application_health() {
    local port=${1:-80}
    local timeout=${2:-10}

    # Basic HTTP health check
    if ! curl -f -s --max-time "$timeout" "http://localhost:${port}/" > /dev/null 2>&1; then
        return 1
    fi

    return 0
}

# Container health check
check_container_health() {
    local container_name="${1:-fes_crm_prod}"

    # Check if container is running
    if ! docker ps --filter "name=$container_name" --filter "status=running" | grep -q "$container_name"; then
        return 1
    fi

    # Check container health status
    local health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "unknown")

    if [[ "$health_status" != "healthy" ]] && [[ "$health_status" != "unknown" ]]; then
        return 1
    fi

    return 0
}

# Database connectivity check
check_database_connectivity() {
    local container_name="${1:-fes_crm_prod}"

    # Test database connection through container
    if ! docker exec "$container_name" npx prisma db pull --schema=./prisma/schema.prisma > /dev/null 2>&1; then
        return 1
    fi

    return 0
}

# Resource usage check
check_resource_usage() {
    local container_name="${1:-fes_crm_prod}"

    # Get container stats
    local stats=$(docker stats --no-stream --format "table {{.MemPerc}}\t{{.CPUPerc}}" "$container_name" 2>/dev/null | tail -1)

    if [[ -z "$stats" ]]; then
        return 1
    fi

    local memory_usage=$(echo "$stats" | awk '{print $1}' | sed 's/%//')
    local cpu_usage=$(echo "$stats" | awk '{print $2}' | sed 's/%//')

    # Check memory usage (alert if > 90%)
    if (( $(echo "$memory_usage > 90" | bc -l) )); then
        warn "High memory usage detected: ${memory_usage}%"
        send_slack_alert "High memory usage detected: ${memory_usage}%" "warning"
    fi

    # Check CPU usage (alert if > 95%)
    if (( $(echo "$cpu_usage > 95" | bc -l) )); then
        warn "High CPU usage detected: ${cpu_usage}%"
        send_slack_alert "High CPU usage detected: ${cpu_usage}%" "warning"
    fi

    return 0
}

# Disk space check
check_disk_space() {
    local threshold=${1:-90}

    # Check disk usage
    local disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')

    if (( disk_usage > threshold )); then
        error "Low disk space: ${disk_usage}% used"
        send_slack_alert "Low disk space warning: ${disk_usage}% used" "critical"
        return 1
    fi

    return 0
}

# Log rotation check
check_log_rotation() {
    local log_dir="${1:-/var/log}"
    local max_size_mb=${2:-1000}

    # Check log file sizes
    find "$log_dir" -name "*.log" -size "+${max_size_mb}M" | while read -r logfile; do
        warn "Large log file detected: $logfile"
        # Rotate log if logrotate is available
        if command -v logrotate > /dev/null 2>&1; then
            logrotate -f /etc/logrotate.conf 2>/dev/null || true
        fi
    done
}

# Performance metrics collection
collect_performance_metrics() {
    local container_name="${1:-fes_crm_prod}"
    local metrics_file="/tmp/fes-crm-metrics.json"

    # Collect container metrics
    local stats=$(docker stats --no-stream --format "json" "$container_name" 2>/dev/null || echo '{}')

    # Collect system metrics
    local system_metrics=$(cat << EOF
{
  "timestamp": "$(date -Iseconds)",
  "hostname": "$(hostname)",
  "uptime": "$(uptime -p)",
  "load_average": "$(uptime | awk -F'load average:' '{print $2}')",
  "memory_total": "$(free -m | awk 'NR==2{print $2}')",
  "memory_used": "$(free -m | awk 'NR==2{print $3}')",
  "disk_usage": "$(df / | tail -1 | awk '{print $5}')",
  "container_stats": $stats
}
EOF
    )

    echo "$system_metrics" > "$metrics_file"
}

# Main monitoring function
monitor_application() {
    local failure_count=0
    local container_name="fes_crm_prod"

    info "Starting application monitoring for $APP_NAME"

    while true; do
        local health_ok=true

        # Application health check
        if ! check_application_health; then
            error "Application health check failed"
            health_ok=false
        fi

        # Container health check
        if ! check_container_health "$container_name"; then
            error "Container health check failed"
            health_ok=false
        fi

        # Database connectivity check
        if ! check_database_connectivity "$container_name"; then
            error "Database connectivity check failed"
            health_ok=false
        fi

        # Resource usage check
        check_resource_usage "$container_name"

        # Disk space check
        check_disk_space

        # Log rotation check
        check_log_rotation

        # Collect performance metrics
        collect_performance_metrics "$container_name"

        if [[ "$health_ok" == "false" ]]; then
            ((failure_count++))
            warn "Health check failed (failure count: $failure_count)"

            if (( failure_count >= ALERT_THRESHOLD )); then
                error "Application health check failed $failure_count times consecutively"
                send_slack_alert "Application health check failed $failure_count times consecutively" "critical"
                send_email_alert "FES CRM Backend Alert" "Application health check failed $failure_count times consecutively"

                # Attempt automatic recovery
                info "Attempting automatic recovery..."
                docker compose -f "$PROJECT_ROOT/docker-compose.prod.yml" restart

                # Reset failure count after recovery attempt
                failure_count=0

                # Wait longer after recovery attempt
                sleep $((HEALTH_CHECK_INTERVAL * 2))
                continue
            fi
        else
            if (( failure_count > 0 )); then
                log "Application health restored after $failure_count failures"
                send_slack_alert "Application health restored after $failure_count failures" "success"
            fi
            failure_count=0
        fi

        sleep "$HEALTH_CHECK_INTERVAL"
    done
}

# Deployment validation function
validate_deployment() {
    local version="${1:-unknown}"
    local timeout=${2:-300}
    local start_time=$(date +%s)

    info "Validating deployment of version $version"

    while true; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))

        if (( elapsed > timeout )); then
            error "Deployment validation timeout after ${timeout}s"
            return 1
        fi

        # Check all health indicators
        if check_application_health && \
           check_container_health && \
           check_database_connectivity; then
            log "Deployment validation successful for version $version"
            send_slack_alert "Deployment validation successful for version $version" "success"
            return 0
        fi

        info "Waiting for deployment to stabilize... (${elapsed}s/${timeout}s)"
        sleep 10
    done
}

# Help function
show_help() {
    cat << EOF
Deployment Monitoring Script for FES CRM Backend

Usage: $0 [COMMAND] [OPTIONS]

COMMANDS:
    monitor                 Start continuous monitoring (default)
    validate VERSION        Validate deployment of specific version
    check                   Run single health check
    metrics                 Collect and display current metrics
    help                    Show this help message

OPTIONS:
    -i, --interval SECONDS  Health check interval (default: 30)
    -t, --threshold COUNT   Alert threshold (default: 3)
    -l, --log-file FILE     Log file path (default: /var/log/fes-crm-monitor.log)

EXAMPLES:
    $0                      # Start monitoring with defaults
    $0 monitor -i 60        # Monitor with 60s interval
    $0 validate abc123      # Validate deployment of version abc123
    $0 check                # Run single health check
    $0 metrics              # Show current metrics

EOF
}

# Parse command line arguments
COMMAND="monitor"

while [[ $# -gt 0 ]]; do
    case $1 in
        monitor|validate|check|metrics|help)
            COMMAND="$1"
            shift
            ;;
        -i|--interval)
            HEALTH_CHECK_INTERVAL="$2"
            shift 2
            ;;
        -t|--threshold)
            ALERT_THRESHOLD="$2"
            shift 2
            ;;
        -l|--log-file)
            LOG_FILE="$2"
            shift 2
            ;;
        *)
            if [[ "$COMMAND" == "validate" ]] && [[ -z "${VERSION:-}" ]]; then
                VERSION="$1"
                shift
            else
                error "Unknown option: $1"
                show_help
                exit 1
            fi
            ;;
    esac
done

# Create log directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"

# Execute command
case "$COMMAND" in
    "monitor")
        monitor_application
        ;;
    "validate")
        if [[ -z "${VERSION:-}" ]]; then
            error "Version is required for validation"
            show_help
            exit 1
        fi
        validate_deployment "$VERSION"
        ;;
    "check")
        info "Running single health check..."
        if check_application_health && check_container_health && check_database_connectivity; then
            log "All health checks passed"
            exit 0
        else
            error "Health checks failed"
            exit 1
        fi
        ;;
    "metrics")
        info "Collecting current metrics..."
        collect_performance_metrics
        cat "/tmp/fes-crm-metrics.json"
        ;;
    "help")
        show_help
        ;;
    *)
        error "Unknown command: $COMMAND"
        show_help
        exit 1
        ;;
esac
