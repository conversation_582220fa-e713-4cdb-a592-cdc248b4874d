<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="120" alt="Nest Logo" /></a>
</p>

[circleci-image]: https://img.shields.io/circleci/build/github/nestjs/nest/master?token=abc123def456
[circleci-url]: https://circleci.com/gh/nestjs/nest

  <p align="center">A progressive <a href="http://nodejs.org" target="_blank">Node.js</a> framework for building efficient and scalable server-side applications.</p>
    <p align="center">
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/v/@nestjs/core.svg" alt="NPM Version" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/l/@nestjs/core.svg" alt="Package License" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/dm/@nestjs/common.svg" alt="NPM Downloads" /></a>
<a href="https://circleci.com/gh/nestjs/nest" target="_blank"><img src="https://img.shields.io/circleci/build/github/nestjs/nest/master" alt="CircleCI" /></a>
<a href="https://coveralls.io/github/nestjs/nest?branch=master" target="_blank"><img src="https://coveralls.io/repos/github/nestjs/nest/badge.svg?branch=master#9" alt="Coverage" /></a>
<a href="https://discord.gg/G7Qnnhy" target="_blank"><img src="https://img.shields.io/badge/discord-online-brightgreen.svg" alt="Discord"/></a>
<a href="https://opencollective.com/nest#backer" target="_blank"><img src="https://opencollective.com/nest/backers/badge.svg" alt="Backers on Open Collective" /></a>
<a href="https://opencollective.com/nest#sponsor" target="_blank"><img src="https://opencollective.com/nest/sponsors/badge.svg" alt="Sponsors on Open Collective" /></a>
  <a href="https://paypal.me/kamilmysliwiec" target="_blank"><img src="https://img.shields.io/badge/Donate-PayPal-ff3f59.svg" alt="Donate us"/></a>
    <a href="https://opencollective.com/nest#sponsor"  target="_blank"><img src="https://img.shields.io/badge/Support%20us-Open%20Collective-41B883.svg" alt="Support us"></a>
  <a href="https://twitter.com/nestframework" target="_blank"><img src="https://img.shields.io/twitter/follow/nestframework.svg?style=social&label=Follow" alt="Follow us on Twitter"></a>
</p>
  <!--[![Backers on Open Collective](https://opencollective.com/nest/backers/badge.svg)](https://opencollective.com/nest#backer)
  [![Sponsors on Open Collective](https://opencollective.com/nest/sponsors/badge.svg)](https://opencollective.com/nest#sponsor)-->

## Description

[Nest](https://github.com/nestjs/nest) framework TypeScript starter repository.

## Project setup

```bash
$ npm install
```

## Compile and run the project

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## Run tests

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## CI/CD Pipeline

This project uses GitHub Actions for continuous integration and deployment with smart npm caching.

### Features
- ✅ **Smart npm caching**: Only installs dependencies when `package-lock.json` changes
- ✅ **Local PostgreSQL**: Uses local database for development/testing
- ✅ **External PostgreSQL**: Uses external database for production
- ✅ **Comprehensive testing**: Unit tests, E2E tests, linting, and formatting
- ✅ **Automated deployment**: Production deployment on push to `main` branch

### Workflows

1. **CI Pipeline** (`.github/workflows/ci.yml`): Runs tests on push/PR to `main` branch
2. **Testing Deployment** (`.github/workflows/deploy.yml`): Deploys to test server on push to `develop` branch
3. **Production Deployment** (`.github/workflows/production.yml`): Deploys to production on push to `main` branch

## Deployment

### Testing Server
Automatically deploys to testing server using Docker when code is pushed to the `develop` branch.
- Uses Docker Compose with local PostgreSQL container

### Production Server
Automatically deploys to production server when code is pushed to the `main` branch using **zero-downtime blue-green deployment**:

1. **Security Scan**: Vulnerability scanning with Trivy
2. **Build & Test**: Comprehensive testing and Docker image build
3. **Blue-Green Deploy**: Start green environment, health checks, traffic switch
4. **Validation**: Post-deployment integration tests
5. **Monitoring**: Continuous health monitoring with alerts
6. **Auto-Rollback**: Automatic rollback on failure

**Zero-Downtime Features:**
- **Blue-Green Deployment**: No service interruption during deployments
- **Health Checks**: Comprehensive application and database monitoring
- **Security Scanning**: Automated vulnerability detection
- **Monitoring & Alerts**: Slack/email notifications
- **Automatic Rollback**: Instant rollback on deployment failure

**Manual Operations:**
```bash
# Zero-downtime deployment
./scripts/zero-downtime-deploy.sh -v latest

# Start monitoring
./scripts/deployment-monitor.sh monitor

# Manual rollback
./scripts/rollback.sh -l  # List backups
./scripts/rollback.sh -v abc123  # Rollback to version
```

**Documentation:**
- [Zero-Downtime Deployment Guide](ZERO-DOWNTIME-DEPLOYMENT.md)
- [Production Setup Guide](PRODUCTION-SETUP.md)

## Resources

Check out a few resources that may come in handy when working with NestJS:

- Visit the [NestJS Documentation](https://docs.nestjs.com) to learn more about the framework.
- For questions and support, please visit our [Discord channel](https://discord.gg/G7Qnnhy).
- To dive deeper and get more hands-on experience, check out our official video [courses](https://courses.nestjs.com/).
- Deploy your application to AWS with the help of [NestJS Mau](https://mau.nestjs.com) in just a few clicks.
- Visualize your application graph and interact with the NestJS application in real-time using [NestJS Devtools](https://devtools.nestjs.com).
- Need help with your project (part-time to full-time)? Check out our official [enterprise support](https://enterprise.nestjs.com).
- To stay in the loop and get updates, follow us on [X](https://x.com/nestframework) and [LinkedIn](https://linkedin.com/company/nestjs).
- Looking for a job, or have a job to offer? Check out our official [Jobs board](https://jobs.nestjs.com).

## Support

Nest is an MIT-licensed open source project. It can grow thanks to the sponsors and support by the amazing backers. If you'd like to join them, please [read more here](https://docs.nestjs.com/support).

## Stay in touch

- Author - [Kamil Myśliwiec](https://twitter.com/kammysliwiec)
- Website - [https://nestjs.com](https://nestjs.com/)
- Twitter - [@nestframework](https://twitter.com/nestframework)

## License

Nest is [MIT licensed](https://github.com/nestjs/nest/blob/master/LICENSE).
