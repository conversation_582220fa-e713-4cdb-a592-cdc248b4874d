import { Module } from '@nestjs/common';
import { EventLoggerService } from './event-logger.service';
import { EventLoggerController } from './event-logger.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { AuthModule } from '../Auth/auth.module';
import { SharedModule } from '../common/modules/shared.module';

@Module({
  imports: [PrismaModule, AuthModule, SharedModule],
  providers: [EventLoggerService],
  controllers: [EventLoggerController],
  exports: [EventLoggerService],
})
export class EventLoggerModule {}
