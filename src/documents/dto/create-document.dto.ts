import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  IsEnum,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { DocumentStatus } from '@prisma/client';

export class CreateDocumentDto {
  @ApiProperty({ description: 'Type of the Document', type: String })
  @IsNotEmpty()
  @IsString()
  type: string;

  @ApiPropertyOptional({
    description: 'ID of the Student this Document belongs to',
    type: String,
  })
  @IsOptional()
  // @IsUUID()
  studentId?: string;

  @ApiPropertyOptional({ description: 'ID of Application', type: String })
  @IsOptional()
  @IsUUID()
  applicationId?: string;

  @ApiPropertyOptional({
    description: 'Optional comment about the document',
    type: String,
  })
  @IsOptional()
  @IsString()
  comment?: string;

  @ApiPropertyOptional({
    description: 'Status of the document',
    enum: DocumentStatus,
    default: DocumentStatus.PENDING,
  })
  @IsOptional()
  @IsEnum(DocumentStatus)
  status?: DocumentStatus;

  @ApiPropertyOptional({
    description: 'Reason for rejection if status is REJECTED',
    type: String,
  })
  @IsOptional()
  @IsString()
  rejectionReason?: string;
}
