import { ConflictException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { CreateUniversityDto } from './dto/create-university.dto';
import { GetUniversityDto } from './dto/get-university.dto';
import { UpdateUniversityDto } from './dto/update-university.dto';
import { UniversitiesController } from './universities.controller';
import { UniversitiesService } from './universities.service';

describe('UniversitiesController', () => {
  let controller: UniversitiesController;
  let service: UniversitiesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UniversitiesController],
      providers: [
        {
          provide: UniversitiesService,
          useValue: {
            createUniversity: jest.fn(),
            getAllUniversities: jest.fn(),
            getUniversityById: jest.fn(),
            updateUniversity: jest.fn(),
            deleteUniversity: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UniversitiesController>(UniversitiesController);
    service = module.get<UniversitiesService>(UniversitiesService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createUniversity', () => {
    it('should create a university successfully', async () => {
      const createUniversityDto: CreateUniversityDto = {
        name: 'Test University',
        description: 'Test Description',
        location: 'Test Location',
        ranking: 1,
      };
      const result: GetUniversityDto = {
        id: '1',
        ...createUniversityDto,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      jest.spyOn(service, 'createUniversity').mockResolvedValue(result);

      expect(await controller.createUniversity(createUniversityDto)).toBe(
        result,
      );
    });

    it('should throw ConflictException if university already exists', async () => {
      const createUniversityDto: CreateUniversityDto = {
        name: 'Existing University',
        description: 'Existing Description',
        location: 'Existing Location',
        ranking: 1,
      };
      jest
        .spyOn(service, 'createUniversity')
        .mockRejectedValue(new ConflictException('University already exists.'));

      await expect(
        controller.createUniversity(createUniversityDto),
      ).rejects.toThrow(ConflictException);
    });
  });

  describe('getAllUniversities', () => {
    it('should get all universities successfully', async () => {
      const result: GetUniversityDto[] = [
        {
          id: '1',
          name: 'Test University 1',
          description: 'Test Description 1',
          location: 'Test Location 1',
          ranking: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '2',
          name: 'Test University 2',
          description: 'Test Description 2',
          location: 'Test Location 2',
          ranking: 2,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];
      jest.spyOn(service, 'getAllUniversities').mockResolvedValue(result);

      expect(await controller.getAllUniversities()).toBe(result);
    });

    it('should return an empty array if no universities exist', async () => {
      jest.spyOn(service, 'getAllUniversities').mockResolvedValue([]);

      expect(await controller.getAllUniversities()).toEqual([]);
    });
  });

  describe('getUniversityById', () => {
    it('should get a university by ID successfully', async () => {
      const result: GetUniversityDto = {
        id: '1',
        name: 'Test University',
        description: 'Test Description',
        location: 'Test Location',
        ranking: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      jest.spyOn(service, 'getUniversityById').mockResolvedValue(result);

      expect(await controller.getUniversityById('1')).toBe(result);
    });

    it('should throw NotFoundException if university not found', async () => {
      jest
        .spyOn(service, 'getUniversityById')
        .mockRejectedValue(new NotFoundException('University not found'));

      await expect(controller.getUniversityById('999')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('updateUniversity', () => {
    it('should update a university successfully', async () => {
      const updateUniversityDto: UpdateUniversityDto = {
        name: 'Updated University',
        description: 'Updated Description',
        location: 'Updated Location',
        ranking: 2,
      };
      const result: GetUniversityDto = {
        id: '1',
        name: updateUniversityDto.name,
        description: updateUniversityDto.description,
        location: updateUniversityDto.location,
        ranking: updateUniversityDto.ranking,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      jest.spyOn(service, 'updateUniversity').mockResolvedValue(result);

      expect(await controller.updateUniversity('1', updateUniversityDto)).toBe(
        result,
      );
    });

    it('should throw NotFoundException if university not found', async () => {
      const updateUniversityDto: UpdateUniversityDto = {
        name: 'Updated University',
      };
      jest
        .spyOn(service, 'updateUniversity')
        .mockRejectedValue(new NotFoundException('University not found'));

      await expect(
        controller.updateUniversity('999', updateUniversityDto),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('deleteUniversity', () => {
    it('should delete a university successfully', async () => {
      jest.spyOn(service, 'deleteUniversity').mockResolvedValue(undefined);

      await expect(controller.deleteUniversity('1')).resolves.toBeUndefined();
    });

    it('should throw NotFoundException if university not found', async () => {
      jest
        .spyOn(service, 'deleteUniversity')
        .mockRejectedValue(new NotFoundException('University not found'));

      await expect(controller.deleteUniversity('999')).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});
