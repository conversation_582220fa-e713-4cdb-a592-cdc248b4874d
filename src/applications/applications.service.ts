import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateApplicationDto } from './dto/create-application.dto';
import { GetApplicationDto } from './dto/get-application.dto';
import { UpdateApplicationDto } from './dto/update.application.dto';
import { GetApplicationsQueryDto } from './dto/get-applications-query.dto';
import { PageDto } from 'src/common/dtos/page.dto';
import { PageMetaDto } from 'src/common/dtos/pageMeta.dto';
import { PageOptionsDto } from 'src/common/dtos/pageOptions.dto';
import { Prisma } from '@prisma/client';
import { DefaultArgs } from '@prisma/client/runtime/library';

@Injectable()
export class ApplicationsService {
  constructor(private readonly prisma: PrismaService) {}

  private readonly offerOrBeyondStatuses = [
    'ACCEPTED',
    'VISA_APPLIED',
    'VISA_ACCEPTED',
    'VISA_REJECTED',
    'ENROLLED',
    'PAID_TUITION',
  ];

  async createApplication(
    createApplicationDto: CreateApplicationDto,
  ): Promise<GetApplicationDto> {
    try {
      const status = createApplicationDto.status || 'APPLIED';

      if (
        createApplicationDto.applicantStatus &&
        !this.offerOrBeyondStatuses.includes(status)
      ) {
        throw new BadRequestException(
          'Applicant status can only be set once application status is ACCEPTED or further (e.g., VISA_APPLIED, VISA_ACCEPTED, etc.)',
        );
      }

      return await this.prisma.application.create({
        data: {
          student: {
            connect: { id: createApplicationDto.studentId },
          },
          university: {
            connect: { id: createApplicationDto.universityId },
          },
          program: {
            connect: { id: createApplicationDto.programId },
          },
          status,
          subStatus: createApplicationDto.subStatus,
          applicantStatus: createApplicationDto.applicantStatus,
          appliedDate: createApplicationDto.appliedDate,
          decisionDate: createApplicationDto.decisionDate,
          visaAppliedDate: createApplicationDto.visaAppliedDate,
          visaDecisionDate: createApplicationDto.visaDecisionDate,
          notes: createApplicationDto.notes,
          lastInstituteAttended: createApplicationDto.lastInstituteAttended,
          lastInstituteDegree: createApplicationDto.lastInstituteDegree,
          interestedDegreeLevel: createApplicationDto.interestedDegreeLevel,
        },
        include: {
          student: true,
          university: true,
          program: true,
          documents: true,
          activityLogs: true,
        },
      });
    } catch (error) {
      throw new HttpException(
        error.message || 'Error creating application',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getApplications(
    query: GetApplicationsQueryDto,
    pageOptionsDto: PageOptionsDto,
  ): Promise<PageDto<GetApplicationDto>> {
    try {
      const {
        studentName,
        universityId,
        programId,
        status,
        subStatus,
        applicantStatus,
        appliedFrom,
        appliedTo,
        counsellorId,
      } = query;

      const [applications, itemCount] = await Promise.all([
        this.prisma.application.findMany({
          where: {
            ...(universityId && { universityId }),
            ...(programId && { programId }),
            ...(status && { status: { equals: status as any } }),
            ...(subStatus && { subStatus: { equals: subStatus as any } }),
            ...(applicantStatus && {
              applicantStatus: { equals: applicantStatus as any },
            }),
            ...(appliedFrom || appliedTo
              ? {
                  appliedDate: {
                    ...(appliedFrom && { gte: new Date(appliedFrom) }),
                    ...(appliedTo && { lte: new Date(appliedTo) }),
                  },
                }
              : {}),
            student: {
              ...(studentName && {
                OR: [
                  { firstName: { contains: studentName, mode: 'insensitive' } },
                  { lastName: { contains: studentName, mode: 'insensitive' } },
                ],
              }),
              ...(counsellorId && { counsellorId }),
            },
          },
          include: {
            student: true,
            university: true,
            program: true,
            documents: true,
            activityLogs: true,
          },
          orderBy: {
            appliedDate: 'desc',
          },
          skip: pageOptionsDto.skip,
          take: pageOptionsDto.take,
        }),
        this.prisma.application.count({
          where: {
            ...(universityId && { universityId }),
            ...(programId && { programId }),
            ...(status && { status: { equals: status as any } }),
            ...(subStatus && { subStatus: { equals: subStatus as any } }),
            ...(applicantStatus && {
              applicantStatus: { equals: applicantStatus as any },
            }),
            ...(appliedFrom || appliedTo
              ? {
                  appliedDate: {
                    ...(appliedFrom && { gte: new Date(appliedFrom) }),
                    ...(appliedTo && { lte: new Date(appliedTo) }),
                  },
                }
              : {}),
            student: {
              ...(studentName && {
                OR: [
                  { firstName: { contains: studentName, mode: 'insensitive' } },
                  { lastName: { contains: studentName, mode: 'insensitive' } },
                ],
              }),
              ...(counsellorId && { counsellorId }),
            },
          },
        }),
      ]);

      const pageMetaDto = new PageMetaDto({
        itemCount,
        pageOptionsDto,
      });

      return new PageDto(applications, pageMetaDto);
    } catch (error) {
      throw new HttpException(
        error.message || 'Error fetching applications',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getApplicationsByStudentId(id: string): Promise<GetApplicationDto[]> {
    try {
      const applications = await this.prisma.application.findMany({
        where: { studentId: id },
        include: {
          student: true,
          university: true,
          program: true,
          documents: true,
          activityLogs: true,
        },
      });

      return applications;
    } catch (error) {
      throw new HttpException(
        error.message || 'Error fetching applications by student',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getApplicationById(id: string): Promise<GetApplicationDto> {
    try {
      const application = await this.prisma.application.findUnique({
        where: { id },
        include: {
          student: true,
          university: true,
          program: true,
          documents: true,
          activityLogs: true,
        },
      });

      if (!application) {
        throw new NotFoundException(`Application with id:${id} not found`);
      }

      return application;
    } catch (error) {
      if (error instanceof NotFoundException) throw error;

      throw new HttpException(
        error.message || 'Error fetching application',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateApplication(
    id: string,
    updateApplicationDto: UpdateApplicationDto,
  ): Promise<GetApplicationDto> {
    try {
      const existing = await this.prisma.application.findUnique({
        where: { id },
      });

      if (!existing) {
        throw new NotFoundException(`Application with id:${id} not found`);
      }

      const nextStatus = updateApplicationDto.status || existing.status;

      if (
        updateApplicationDto.applicantStatus &&
        !this.offerOrBeyondStatuses.includes(nextStatus)
      ) {
        throw new BadRequestException(
          'Applicant status can only be set once application status is ACCEPTED or further (e.g., VISA_APPLIED, VISA_ACCEPTED, etc.)',
        );
      }

      const updatedApplication = await this.prisma.application.update({
        where: { id },
        data: {
          status: updateApplicationDto.status,
          subStatus: updateApplicationDto.subStatus,
          applicantStatus: updateApplicationDto.applicantStatus,
          appliedDate: updateApplicationDto.appliedDate,
          decisionDate: updateApplicationDto.decisionDate,
          visaAppliedDate: updateApplicationDto.visaAppliedDate,
          visaDecisionDate: updateApplicationDto.visaDecisionDate,
          notes: updateApplicationDto.notes,
        },
        include: {
          student: true,
          university: true,
          program: true,
          documents: true,
          activityLogs: true,
        },
      });

      return updatedApplication;
    } catch (error) {
      if (error instanceof NotFoundException) throw error;

      throw new HttpException(
        error.message || 'Error updating application',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteApplication(id: string): Promise<GetApplicationDto> {
    try {
      const application = await this.prisma.application.findUnique({
        where: { id },
        include: {
          student: true,
          university: true,
          program: true,
        },
      });

      if (!application) {
        throw new NotFoundException(`Application with id:${id} not found`);
      }

      await this.prisma.application.delete({ where: { id } });

      return application;
    } catch (error) {
      if (error instanceof NotFoundException) throw error;

      throw new HttpException(
        error.message || 'Error deleting application',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
