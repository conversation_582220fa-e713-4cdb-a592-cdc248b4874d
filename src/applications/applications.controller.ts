import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Permissions } from 'src/Auth/decorators/permissions.decorator';
import { ApiRolesPermissions } from 'src/Auth/decorators/roles-permissions.swagger.decorator';
import { Roles } from 'src/Auth/decorators/roles.decorator';
import { ApplicationsService } from './applications.service';
import { CreateApplicationDto } from './dto/create-application.dto';
import { GetApplicationDto } from './dto/get-application.dto';
import { UpdateApplicationDto } from './dto/update.application.dto';
import { GetApplicationsQueryDto } from './dto/get-applications-query.dto';
import { PageOptionsDto } from 'src/common/dtos/pageOptions.dto';
import { ApiPaginatedResponse } from 'src/common/decorators/api-paginated-response.decorator';
import {
  ApplicationStatus,
  ApplicationSubStatus,
  ApplicantStatus,
} from '@prisma/client';

@ApiTags('Applications')
@ApiBearerAuth()
@Controller('applications')
export class ApplicationsController {
  constructor(private readonly applicationsService: ApplicationsService) {}

  @Post()
  @Permissions('create:applications')
  @ApiResponse({
    status: 201,
    description: 'The Application has been successfully created.',
    type: GetApplicationDto,
  })
  async createApplication(
    @Body() createApplicationDto: CreateApplicationDto,
  ): Promise<GetApplicationDto> {
    return this.applicationsService.createApplication(createApplicationDto);
  }

  @Get()
  @Permissions('read:applications')
  @ApiPaginatedResponse(
    GetApplicationDto,
    'The list of Applications has been successfully retrieved.',
  )
  @ApiQuery({
    name: 'studentName',
    required: false,
    type: String,
    description: 'Filter by student name',
  })
  @ApiQuery({
    name: 'universityId',
    required: false,
    type: String,
    description: 'Filter by university ID',
  })
  @ApiQuery({
    name: 'programId',
    required: false,
    type: String,
    description: 'Filter by program ID',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: ApplicationStatus,
    description: 'Filter by application status',
  })
  @ApiQuery({
    name: 'subStatus',
    required: false,
    enum: ApplicationSubStatus,
    description: 'Filter by application sub-status',
  })
  @ApiQuery({
    name: 'applicantStatus',
    required: false,
    enum: ApplicantStatus,
    description: 'Filter by applicant status',
  })
  @ApiQuery({
    name: 'counsellorId',
    required: false,
    type: String,
    description: 'Filter by counsellor ID',
  })
  @ApiQuery({
    name: 'appliedFrom',
    required: false,
    type: String,
    description: 'Filter by applied date from (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'appliedTo',
    required: false,
    type: String,
    description: 'Filter by applied date to (YYYY-MM-DD)',
  })
  async getAllApplications(
    @Query() query: GetApplicationsQueryDto,
    @Query() pageOptionsDto: PageOptionsDto,
  ) {
    return this.applicationsService.getApplications(query, pageOptionsDto);
  }

  @Get('/student/:id')
  @Permissions('read:applications')
  @ApiResponse({
    status: 200,
    description: 'The list of all Applications of a Student.',
    type: [GetApplicationDto],
  })
  async getApplicationsByStudentId(
    @Param('id') id: string,
  ): Promise<GetApplicationDto[]> {
    return this.applicationsService.getApplicationsByStudentId(id);
  }

  @Get(':id')
  @Permissions('read:applications')
  @ApiResponse({
    status: 200,
    description: 'The Application has been successfully retrieved.',
    type: GetApplicationDto,
  })
  async getApplicationById(
    @Param('id') id: string,
  ): Promise<GetApplicationDto> {
    return this.applicationsService.getApplicationById(id);
  }

  @Patch(':id')
  @Permissions('update:applications')
  @ApiResponse({
    status: 200,
    description: 'The Application has been successfully updated.',
    type: GetApplicationDto,
  })
  async updateApplication(
    @Param('id') id: string,
    @Body() updateApplicationDto: UpdateApplicationDto,
  ): Promise<GetApplicationDto> {
    return this.applicationsService.updateApplication(id, updateApplicationDto);
  }

  @Delete(':id')
  @Permissions('delete:applications')
  @ApiResponse({
    status: 204,
    description: 'The Application has been successfully deleted.',
    type: GetApplicationDto,
  })
  async deleteApplication(@Param('id') id: string): Promise<GetApplicationDto> {
    return this.applicationsService.deleteApplication(id);
  }
}
