import {
  IsDateString,
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  ApplicationSubStatus,
  ApplicationStatus,
  ApplicantStatus,
} from '@prisma/client';

export class GetApplicationsQueryDto {
  @IsOptional()
  @IsString()
  @ApiPropertyOptional({ description: 'Filter by student name' })
  studentName?: string;

  @IsOptional()
  @IsUUID()
  @ApiPropertyOptional({ description: 'Filter by university ID' })
  universityId?: string;

  @IsOptional()
  @IsUUID()
  @ApiPropertyOptional({ description: 'Filter by program ID' })
  programId?: string;

  @IsOptional()
  @IsEnum(ApplicationStatus)
  @ApiPropertyOptional({
    description: 'Filter by application status',
    enum: ApplicationStatus,
    example: ApplicationStatus.APPLIED,
  })
  status?: ApplicationStatus;

  @IsOptional()
  @IsEnum(ApplicationSubStatus)
  @ApiPropertyOptional({
    description: 'Filter by sub-status',
    enum: ApplicationSubStatus,
  })
  subStatus?: ApplicationSubStatus;

  @IsOptional()
  @IsEnum(ApplicantStatus)
  @ApiPropertyOptional({
    description: 'Filter by applicant status',
    enum: ApplicantStatus,
    example: ApplicantStatus.OFFER_ACCEPTED,
  })
  applicantStatus?: ApplicantStatus;

  @IsOptional()
  @IsUUID()
  @ApiPropertyOptional({ description: 'Filter by counsellor ID' })
  counsellorId?: string;

  @IsOptional()
  @IsDateString()
  @ApiPropertyOptional({ description: 'Filter by applied date from' })
  appliedFrom?: string;

  @IsOptional()
  @IsDateString()
  @ApiPropertyOptional({ description: 'Filter by applied date to' })
  appliedTo?: string;
}
