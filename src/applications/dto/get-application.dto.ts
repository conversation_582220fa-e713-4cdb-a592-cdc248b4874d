import { ApiProperty } from '@nestjs/swagger';
import { ApplicationStatus, ApplicantStatus } from '@prisma/client';
import {
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { StudentDto } from 'src/employees/dto/student.dto';
import { ProgramDto } from 'src/programs/dto/program.dto';
import { GetUniversityDto } from 'src/universities/dto/get-university.dto';
import { UserDto } from 'src/users/dto/user.dto';

export class GetApplicationDto {
  @ApiProperty({
    description: 'The ID of the application',
    required: true,
    type: String,
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'The ID of the Student applying',
    required: true,
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  studentId: string;

  @ApiProperty({
    description: 'The ID of the University applied to',
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  universityId: string;

  @ApiProperty({
    description: 'The ID of the Program applied for',
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  programId: string;

  @ApiProperty({
    description: 'The status of the Application',
    required: true,
    enum: ApplicationStatus,
  })
  @IsNotEmpty()
  @IsEnum(ApplicationStatus)
  status: ApplicationStatus;

  @IsOptional()
  @IsEnum(ApplicantStatus)
  @ApiProperty({
    description: 'The status representing action taken by applicant',
    enum: ApplicantStatus,
    required: false,
  })
  applicantStatus?: ApplicantStatus;

  @ApiProperty({
    description: 'The date when the Application was submitted',
    type: Date,
    required: false,
  })
  @IsOptional()
  @IsDate()
  appliedDate?: Date;

  @ApiProperty({
    description: 'The date when a decision was made on the Application',
    type: Date,
    required: false,
  })
  @IsOptional()
  @IsDate()
  decisionDate?: Date;

  @ApiProperty({
    description: 'The date when visa was applied for',
    type: Date,
    required: false,
  })
  @IsOptional()
  @IsDate()
  visaAppliedDate?: Date;

  @ApiProperty({
    description: 'The date when a decision was made on the visa Application',
    type: Date,
    required: false,
  })
  @IsOptional()
  @IsDate()
  visaDecisionDate?: Date;

  @ApiProperty({
    description: 'Any additional notes',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    description: 'The creation date of the Application',
    type: Date,
  })
  createdAt: Date;

  @ApiProperty({
    description: 'The last update date of the Application',
    type: Date,
  })
  updatedAt: Date;

  @ApiProperty({ type: () => UserDto, required: false })
  @IsOptional()
  student?: StudentDto;

  @ApiProperty({ type: () => GetUniversityDto, required: false })
  @IsOptional()
  university?: GetUniversityDto;

  @ApiProperty({ type: () => ProgramDto, required: false })
  @IsOptional()
  program?: ProgramDto;
}
