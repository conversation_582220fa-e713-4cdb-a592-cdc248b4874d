import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  ApplicationStatus,
  ApplicationSubStatus,
  ApplicantStatus,
  DegreeLevel,
} from '@prisma/client';
import { Type } from 'class-transformer';
import {
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateApplicationDto {
  @ApiProperty({ description: 'The ID of the Student applying', type: String })
  @IsNotEmpty()
  @IsString()
  studentId: string;

  @ApiProperty({
    description: 'The ID of the University applied to',
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  universityId: string;

  @ApiProperty({
    description: 'The ID of the Program applied for',
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  programId: string;

  @ApiProperty({
    description: 'Name of the last institute attended',
    type: String,
  })
  @IsNotEmpty()
  @IsString()
  lastInstituteAttended: string;

  @ApiProperty({ description: 'Degree from the last institute', type: String })
  @IsNotEmpty()
  @IsString()
  lastInstituteDegree: string;

  @ApiProperty({ description: 'Degree level interested in', enum: DegreeLevel })
  @IsNotEmpty()
  @IsEnum(DegreeLevel)
  interestedDegreeLevel: DegreeLevel;

  @ApiPropertyOptional({ description: 'Referral code if any', type: String })
  @IsOptional()
  @IsString()
  referralCode?: string;

  @ApiPropertyOptional({
    description: 'The status of the Application',
    enum: ApplicationStatus,
  })
  @IsOptional()
  @IsEnum(ApplicationStatus)
  status?: ApplicationStatus;

  @ApiPropertyOptional({
    description: 'The substatus of the Application',
    enum: ApplicationSubStatus,
  })
  @IsOptional()
  @IsEnum(ApplicationSubStatus)
  subStatus?: ApplicationSubStatus;

  @ApiPropertyOptional({
    description: 'The applicant status',
    enum: ApplicantStatus,
  })
  @IsOptional()
  @IsEnum(ApplicantStatus)
  applicantStatus?: ApplicantStatus;

  @ApiPropertyOptional({
    description: 'Date when application was submitted',
    type: Date,
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  appliedDate?: Date;

  @ApiPropertyOptional({
    description: 'Date when decision was made',
    type: Date,
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  decisionDate?: Date;

  @ApiPropertyOptional({ description: 'Visa applied date', type: Date })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  visaAppliedDate?: Date;

  @ApiPropertyOptional({ description: 'Visa decision date', type: Date })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  visaDecisionDate?: Date;

  @ApiPropertyOptional({ description: 'Additional notes', type: String })
  @IsOptional()
  @IsString()
  notes?: string;
}
