import {
  ConflictException,
  HttpException,
  HttpStatus,
  Injectable,
} from '@nestjs/common';

import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import * as bcrypt from 'bcrypt';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateAnEmployeeDto } from './dto/create-an-employee.dto';
import { GetAnEmployeeDto } from './dto/get-an-employee.dto';
import { UpdateAnEmployeeDto } from './dto/update-an-employee.dto';

@Injectable()
export class EmployeesService {
  constructor(private prisma: PrismaService) {}

  /* 
    No need to check for the field values because it will already be checked
    in our DTOs. Checks like, value should not be empty or optional and contain
    only a certain datatypes, will be validated in our DTOs. Hence reducing our
    code
  */

  async createAnEmployee(
    createAnEmployeeDto: CreateAnEmployeeDto,
  ): Promise<GetAnEmployeeDto> {
    try {
      // Check if the email already exists in the User table
      const existingUser = await this.prisma.user.findUnique({
        where: { email: createAnEmployeeDto.email },
      });
      if (existingUser) {
        throw new ConflictException('Email already exists');
      }

      const hashedPassword = await bcrypt.hash(
        createAnEmployeeDto.password,
        10,
      );

      const employee = await this.prisma.$transaction(async (prisma) => {
        const user = await prisma.user.create({
          data: {
            email: createAnEmployeeDto.email,
            name: `${createAnEmployeeDto.firstName} ${createAnEmployeeDto.lastName}`,
            password: hashedPassword,
            status: 'ACTIVE',
          },
        });

        return await prisma.employee.create({
          data: {
            userId: user.id,
            firstName: createAnEmployeeDto.firstName,
            lastName: createAnEmployeeDto.lastName,
            phone: createAnEmployeeDto.phone,
          },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            phone: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                status: true,
              },
            },
          },
        });
      });

      return employee;
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Email already exists');
        }
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAllEmployees(): Promise<GetAnEmployeeDto[]> {
    try {
      const response = await this.prisma.employee.findMany({
        select: {
          id: true,
          firstName: true,
          lastName: true,
          phone: true,
          officeId: true,
          regionId: true,
          students: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              roles: true,
            },
          },
        },
      });

      return response;
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getEmployeeById(id: string): Promise<GetAnEmployeeDto> {
    try {
      const employee = await this.prisma.employee.findUnique({
        where: { id },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          phone: true,
          officeId: true,
          regionId: true,
          students: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              roles: true,
            },
          },
        },
      });

      if (!employee) {
        throw new HttpException(
          `Employee with id ${id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      return employee;
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateAnEmployee(
    id: string,
    updateEmployeeDto: UpdateAnEmployeeDto,
  ): Promise<GetAnEmployeeDto> {
    try {
      const existingEmployee = await this.prisma.employee.findUnique({
        where: { id },
        include: { user: true },
      });

      if (!existingEmployee) {
        throw new HttpException(
          `Employee with id ${id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      const response = await this.prisma.employee.update({
        where: { id },
        data: {
          firstName: updateEmployeeDto.firstName,
          lastName: updateEmployeeDto.lastName,
          phone: updateEmployeeDto.phone,
          user: {
            update: {
              name: `${updateEmployeeDto.firstName} ${updateEmployeeDto.lastName}`,
            },
          },
        },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          phone: true,
          officeId: true,
          regionId: true,
          students: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              roles: true,
            },
          },
        },
      });

      return response;
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteAnEmployee(id: string): Promise<GetAnEmployeeDto> {
    try {
      const employee = await this.prisma.employee.findUnique({
        where: { id },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          phone: true,
          officeId: true,
          regionId: true,
          students: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              roles: true,
            },
          },
        },
      });

      if (!employee || employee.user.status === 'DELETED') {
        throw new HttpException(
          `Employee with id ${id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      // await this.prisma.employee.delete({ where: { id } });

      // await this.prisma.user.delete({ where: { id: employee.user.id } });

      // Soft delete
      await this.prisma.user.update({
        where: { id: employee.user.id },
        data: { status: 'DELETED' },
      });

      return employee;
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateEmployeeRoels(
    id: string,
    updatedRoles: any,
  ): Promise<GetAnEmployeeDto> {
    try {
      const employee = await this.prisma.employee.findUnique({
        where: { id },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          phone: true,
          officeId: true,
          regionId: true,
          students: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              roles: true,
            },
          },
        },
      });

      if (!employee || employee.user.status === 'DELETED') {
        throw new HttpException(
          `Employee with id ${id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      await this.prisma.user.update({
        where: { id: employee.user.id },
        data: {
          roles: {
            set: [], // optional: remove all current roles
            connect: updatedRoles.map((roleId) => ({ id: roleId })),
          },
        },
      });

      const updatedEmployee = await this.prisma.employee.findUnique({
        where: { id },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          phone: true,
          officeId: true,
          regionId: true,
          students: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              status: true,
              roles: true,
            },
          },
        },
      });

      return updatedEmployee;
    } catch (error) {
      if (error.status === HttpStatus.NOT_FOUND) {
        throw error;
      }
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
